<?php

namespace App\Http\Controllers;

use App\Models\BaseDevice;
use App\Models\BasePeople;
use App\Models\BasePrimise;
use App\Models\BaseUser;
use App\Models\WarnRule;
use Illuminate\Support\Facades\Validator;
use Cache;
use DB;
use Illuminate\Validation\Rule;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SaveInfoController extends Controller
{
    public function saveUserInfo(Request $request)
    {
        $regx = '/(^1\d{10}$|^(0\d{2,3}-?|0\d{2,3} )?[1-9]\d{6}?$)/';
        $regpwd = '/^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/';
        $validator = Validator::make($request->all(), [
            'id' => 'exists:base_users,id|required',
            'name' => 'required|regex:/\p{Han}/u|max:20|min:2',
            'primise' => [
                "required",
                Rule::exists('base_primise', 'id')->where(function ($query) {
                    return $query->where('id', '!=', "1");
                })
            ],
            'password' => ["exclude_if:password,null", "max:20", "min:2", "regex:{$regpwd}"],
            'deleted' => ["required", Rule::in(false, true)],
            'phone' => ["exclude_if:phone,无", "required", "regex:{$regx}"],
            'town' => 'required|regex:/\p{Han}/u|max:80|min:2',
        ], [
                'id.required' => "参数缺失id",
                'id.exists' => "此id不存在",
                'name.required' => "请输入昵称",
                'name.regex' => "昵称只能是中文",
                'name.max' => "昵称长度不得超过20",
                'name.min' => "昵称长度不得低于2",
                'primise.required' => "请选择角色",
                'primise.exists' => "此角色不存在",

                'password.max' => "密码长度不得超过20位",
                'password.min' => "密码长度不得低于2位",
                'password.regex' => '密码强度校验失败，最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符',

                'deleted.required' => "请选择是否禁用！",
                'deleted.in' => "是否禁用只能为是或否！",
                'phone.regex' => "电话格式不对！",
                'phone.required' => "请填写电话！没有的话就填写无",
                'town.required' => "所属乡镇未填写！",
                'town.regex' => "所属乡镇只能为中文！",
                'town.max' => "所属乡镇的长度不得超过80个字哦！",
                'town.min' => "所属乡镇的长度不得低于两个字哦！",
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }

        $data['name'] = $request->name ?? '';
        $data['phone'] = $request->phone ?? '';
        $data['town'] = $request->town ?? '';
        $data['deleted'] = $request->deleted ?? 0;
        $data['primise'] = $request->primise;
        $data['password'] = md5($request->password);

        $db = new BaseUser();
        $isset_user = $db
            ->where('id', '=', $request->id)
            ->first();
        if (empty($isset_user)) {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "用户不存在";
            return $res;
        }


        if (stristr($request->password, $isset_user->USERNAME)) {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "密码中不可以包含用户名";
            return $res;
        }


        $res_sql = $db
            ->where('id', '=', $request->id)
            ->where('primise', '!=', '1')
            ->Update($data);

        if (!$res_sql) {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "数据未更新！";
            return $res;
        } else {
            $res['infor'] = "success";
            $res['msg'] = "数据更新成功";
            $res['code'] = 0;
            return $res;
        }
    }

    public function addUserInfo(Request $request)
    {
        if ($request->user()->PRIMISE != 1) { //非超级管理员
            $res['code'] = 101;
            $res['msg'] = "对不起，您不是管理员，没有权限操作！";
            return $res;
        }
        $regx = '/(^1\d{10}$|^(0\d{2,3}-?|0\d{2,3} )?[1-9]\d{6}?$)/';
        $regpwd = '/^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/';
        $validator = Validator::make($request->all(), [
            'id' => 'unique:base_users,id|required',
            'username' => 'unique:base_users,username|required|max:20|min:3',
            'name' => 'required|regex:/\p{Han}/u|max:20|min:2',
            'primise' => [
                "required",
                Rule::exists('base_primise', 'id')->where(function ($query) {
                    return $query->where('id', '!=', "1");
                })
            ],
            'password' => ["required", "max:20", "min:2", "regex:{$regpwd}"],
            'deleted' => ["required", Rule::in(false, true)],
            'phone' => ["exclude_if:phone,无", "required", "regex:{$regx}"],
            'town' => 'required|regex:/\p{Han}/u|max:80|min:2',
        ], [
                'id.required' => "参数缺失id",
                'id.unique' => "此id重复",
                'username.required' => "请输入用户名",
                'username.unique' => "此用户名重复",
                'username.max' => "用户名长度不得超过20",
                'username.min' => "用户名长度不得低于3",
                'name.required' => "请输入昵称",
                'name.regex' => "昵称只能是中文",
                'name.max' => "昵称长度不得超过20",
                'name.min' => "昵称长度不得低于2",
                'primise.required' => "请选择角色",
                'primise.exists' => "此角色不存在",
                'password.required' => "请输入密码",
                'password.max' => "密码长度不得超过20位",
                'password.min' => "密码长度不得低于2位",
                'password.regex' => '密码强度校验失败，最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符',
                'deleted.required' => "请选择是否禁用！",
                'deleted.in' => "是否禁用只能为是或否！",
                'phone.regex' => "电话格式不对！",
                'phone.required' => "请填写电话！没有的话就填写无",
                'town.required' => "所属乡镇未填写！",
                'town.regex' => "所属乡镇只能为中文！",
                'town.max' => "所属乡镇的长度不得超过80个字哦！",
                'town.min' => "所属乡镇的长度不得低于两个字哦！",
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        if (stristr($request->password, $request->username)) {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "密码中不可以包含用户名";
            return $res;
        }
        $data['name'] = $request->name ?? '';
        $data['username'] = $request->username ?? '';
        $data['id'] = $request->id ?? '';
        $data['phone'] = $request->phone ?? '';
        $data['town'] = $request->town ?? '';
        $data['deleted'] = $request->deleted ?? 0;
        $data['primise'] = $request->primise;
        $data['password'] = md5($request->password);
        $db = new BaseUser();

        $res_sql = $db->insert($data);
        if ($res_sql) {
            $res['infor'] = "success";
            $res['msg'] = "添加成功！";
            $res['code'] = 0;
            return $res;
        } else {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "数据添加失败！";
            return $res;
        }
    }

    public function saveOldmanInfo(Request $request)
    {
        $regx = '/(^1\d{10}$|^(0\d{2,3}-?|0\d{2,3} )?[1-9]\d{6}?$)/';
        $idcard = '/^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/';
        $validator = Validator::make($request->all(), [
            'id' => [
                function ($attribute, $value, $fail) use ($request) {
                    $basePeopleExists = DB::table('base_people')
                        ->where('id', $value)
                        ->exists();

                    if (!$basePeopleExists) {
                        $fail('属性' . $attribute . ' 无效');
                    }
                    if ($request->deleted === true) {
                        $baseDeviceExists = DB::table('base_device')
                            ->where('people_id', $value)
                            ->where(function ($query) {
                                        $query->whereNull('deleted')
                                            ->orWhere('deleted', 0);
                                    })
                            ->exists();
                        if ($baseDeviceExists) {
                            $fail('禁用此人前，请先取消绑定设备，或者禁用此人已绑定的设备');
                        }
                    }

                },
                'min:1',
                'required'
            ],
            'deleted' => ["required", Rule::in(false, true)],
            'name' => 'required|regex:/\p{Han}/u|max:8|min:2',
            'phone' => ["exclude_if:phone,无", "required", "regex:{$regx}"],
            'relation_name' => 'exclude_if:relation_name,无|required|regex:/\p{Han}/u|max:4|min:2',
            'relation_phone' => ["exclude_if:relation_phone,无", "required", "regex:{$regx}"],
            'cadre_name' => 'exclude_if:cadre_name,无|required|regex:/\p{Han}/u|max:4|min:2',
            'cadre_phone' => ["exclude_if:cadre_phone,无", "required", "regex:{$regx}"],
            'town' => ["required"],
            'big_village' => ["required"],
            'idcard' => ["required", "regex:{$idcard}"],
        ], [
                'id.required' => "参数缺失u",
                'id.exists' => "特困人员不存在",
                'id.min' => 'ID长度最低为1',
                'deleted.required' => "请选择是否禁用！",
                'deleted.in' => "是否禁用只能为是或否！",
                'relation_name.required' => "监护人姓名未填写,如果没有，请填写【无】！",
                'relation_name.regex' => "监护人姓名格式不对！",
                'relation_name.max' => "监护人姓名长度不得超过四个字哦！",
                'relation_name.min' => "监护人姓名长度不得低于两个字哦！",
                'name.required' => "特困人员姓名未填写！",
                'name.regex' => "特困人员姓名格式不对！",
                'name.max' => "特困人员姓名不得超过八个字哦！",
                'name.min' => "特困人员姓名不得低于两个字哦！",
                'relation_phone.required' => "监护人电话未填写,如果没有，请填写【无】！",
                'relation_phone.regex' => "监护人电话格式不对！",
                'cadre_name.required' => "村联系人姓名未填写,如果没有，请填写【无】！",
                'cadre_name.regex' => "村联系人姓名格式不对！",
                'cadre_name.max' => "村联系人姓名长度不得超过四个字哦！",
                'cadre_name.min' => "村联系人姓名长度不得小于两个字哦！",
                'cadre_phone.required' => "村联系人电话未填写,如果没有，请填写【无】！",
                'cadre_phone.regex' => "村联系人电话格式不对！",
                'phone.required' => "特困人员电话未填写！",
                'phone.regex' => "特困人员电话格式不对！",
                'town.required' => '必须填写乡镇！',
                'big_village.required' => '必须填写行政村！',
                'idcard.required' => '必须填写身份证号！',
                'idcard.regex' => '身份证号格式不对！',
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $data['name'] = $request->name ?? '';
        $data['phone'] = $request->phone ?? null;
        $data['deleted'] = $request->deleted ?? 0;
        $data['address'] = $request->address ?? null;
        $data['address_info'] = $request->address_info ?? null;
        $data['cadre_name'] = $request->cadre_name ?? null;
        $data['cadre_phone'] = $request->cadre_phone ?? null;
        $data['relation_name'] = $request->relation_name ?? null;
        $data['relation_phone'] = $request->relation_phone ?? null;
        $data['town'] = $request->town ?? null;
        $data['big_village'] = $request->big_village ?? null;
        $data['idcard'] = $request->idcard ?? null;
        $db = new BasePeople();

        $res_sql = $db
            ->where('id', '=', $request->id)
            ->Update($data);

        if (!$res_sql) {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "数据未更新！";
            return $res;
        } else {
            $res['infor'] = "success";
            $res['msg'] = "数据更新成功";
            $res['code'] = 0;
            return $res;
        }
    }

    public function addOldmanInfo(Request $request)
    {
        $regx = '/(^1\d{10}$|^(0\d{2,3}-?|0\d{2,3} )?[1-9]\d{6}?$)/';
        $regx_idcard = '/^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/';
        $validator = Validator::make($request->all(), [
            'id' => 'unique:base_people,id|required',
            'name' => 'required|regex:/\p{Han}/u|max:8|min:2',
            'deleted' => ["required", Rule::in(false, true)],
            'phone' => ["exclude_if:phone,无", "required", "regex:{$regx}"],
            'address' => 'required|regex:/\p{Han}/u|max:50|min:2',
            'town' => 'required|regex:/\p{Han}/u|max:8|min:2',
            'big_village' => 'required|regex:/\p{Han}/u|max:8|min:2',
            'idcard' => ["unique:base_people,idcard", "required", "regex:{$regx_idcard}"],
            'relation_name' => 'exclude_if:relation_name,无|required|regex:/\p{Han}/u|max:4|min:2',
            'relation_phone' => ["exclude_if:relation_phone,无", "required", "regex:{$regx}"],
            'cadre_name' => 'exclude_if:cadre_name,无|required|regex:/\p{Han}/u|max:4|min:2',
            'cadre_phone' => ["exclude_if:cadre_phone,无", "required", "regex:{$regx}"],
        ], [
                'id.required' => "参数缺失u",
                'id.unique' => "特困人员ID已存在",
                'deleted.required' => "请选择是否禁用！",
                'deleted.in' => "是否禁用只能为是或否！",
                'relation_name.required' => "监护人姓名未填写,如果没有，请填写【无】！",
                'relation_name.regex' => "监护人姓名格式不对！",
                'relation_name.max' => "监护人姓名长度不得超过四个字哦！",
                'relation_name.min' => "监护人姓名长度不得低于两个字哦！",
                'name.required' => "特困人员姓名未填写！",
                'name.regex' => "特困人员姓名格式不对！",
                'name.max' => "特困人员姓名不得超过八个字哦！",
                'name.min' => "特困人员姓名不得低于两个字哦！",
                'relation_phone.required' => "监护人电话未填写,如果没有，请填写【无】！",
                'relation_phone.regex' => "监护人电话格式不对！",
                'cadre_name.required' => "村联系人姓名未填写,如果没有，请填写【无】！",
                'cadre_name.regex' => "村联系人姓名格式不对！",
                'cadre_name.max' => "村联系人姓名长度不得超过四个字哦！",
                'cadre_name.min' => "村联系人姓名长度不得小于两个字哦！",
                'cadre_phone.required' => "村联系人电话未填写,如果没有，请填写【无】！",
                'cadre_phone.regex' => "村联系人电话格式不对！",
                'phone.required' => "特困人员电话未填写！",
                'phone.regex' => "特困人员电话格式不对！",
                'address.required' => "特困人员家庭地址未填写！",
                'address.regex' => "特困人员家庭地址只能为中文！",
                'address.max' => "特困人员家庭地址长度不得超过50个字哦！",
                'address.min' => "特困人员家庭地址长度不得低于两个字哦！",
                'town.required' => "特困人员所属乡镇未填写！",
                'town.regex' => "特困人员所属乡镇只能为中文！",
                'town.max' => "特困人员所属乡镇的长度不得超过8个字哦！",
                'town.min' => "特困人员所属乡镇的长度不得低于两个字哦！",
                'big_village.required' => "特困人员所属行政村未填写！",
                'big_village.regex' => "特困人员所属行政村只能为中文！",
                'big_village.max' => "特困人员所属行政村的长度不得超过8个字哦！",
                'big_village.min' => "特困人员所属行政村的长度不得低于两个字哦！",
                'idcard.required' => "特困人员身份证号未填写！",
                'idcard.regex' => "特困人员身份证号格式不正确！",
                'idcard.unique' => "特困人员身份证号已存在，不得重复！",
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $data['name'] = $request->name ?? '';
        $data['id'] = $request->id ?? '';
        $data['phone'] = $request->phone ?? null;
        $data['deleted'] = $request->deleted ?? 0;
        $data['idcard'] = $request->idcard ?? null;
        $data['town'] = $request->town ?? null;
        $data['big_village'] = $request->big_village ?? null;
        $data['address'] = $request->address ?? null;
        $data['address_info'] = $request->address_info ?? null;
        $data['cadre_name'] = $request->cadre_name ?? null;
        $data['cadre_phone'] = $request->cadre_phone ?? null;
        $data['relation_name'] = $request->relation_name ?? null;
        $data['relation_phone'] = $request->relation_phone ?? null;
        $db = new BasePeople();

        $res_sql = $db->insert($data);
        if ($res_sql) {
            $res['infor'] = "success";
            $res['msg'] = "保存成功！";
            $res['code'] = 0;
            return $res;
        } else {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "数据添加失败！";
            return $res;
        }
    }


    public function saveDeviceInfo(Request $request)
    {
        $enumValues = Cache::remember('device_type_enum_values', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM base_device WHERE Field = 'device_type'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });
        $validator = Validator::make($request->all(), [
            'id' => 'exists:base_device,id|required',
            'name' => 'required|regex:/\p{Han}/u|max:30|min:2',
            'device_id' => [
                "required",
                "min:2",
                Rule::unique('base_device', 'device_id')->ignore($request->id)
            ],
            'device_type' => [
                'required',
                'regex:/\p{Han}/u',
                Rule::in($enumValues),
                // 添加这一行,
            ],
            'address' => ["required", "regex:/\p{Han}/u", "max:80", "min:2"],
            'deleted' => ["required", Rule::in(false, true)],
            'people_id' => [
                "required",
                Rule::exists('base_people', 'id')->where(function ($query) {
                    return $query->where('deleted', '=', "0");
                })
            ]
        ], [
                'id.required' => "参数缺失u",
                'id.exists' => "设备ID不存在",
                'deleted.required' => "请选择是否禁用！",
                'deleted.in' => "是否禁用只能为是或否！",
                'name.required' => "设备名称未填写！",
                'name.regex' => "设备名称格式不对，必须为中文！",
                'name.max' => "设备名称长度不得超过30个字哦！",
                'name.min' => "设备名称长度不得低于2个字哦！",
                'device_id.required' => "设备硬件编号未填写！",
                'device_id.min' => "设备硬件编号长度低于2个！",
                'device_id.unique' => "设备硬件编号不能和别的设备重复！",

                'device_type.required' => "请选择设备类型！",
                'device_type.regex' => "设备类型必须是中文！",
                'device_type.in' => "设备类型不是[" . join(',', $enumValues) . "]中的一个！",
                'address.regex' => "设备安装地址必须填写中文！",
                'address.required' => "必须填写设备安装地址！",
                'address.max' => "设备安装地址长度不得超过80个字符！",
                'address.min' => "设备安装地址长度不得低于2个字符！",
                'people_id.required' => "请选择绑定人员！",
                'people_id.exists' => "选择的绑定人员不正确！此人不存在系统中或者已被禁用！",
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $data['name'] = $request->name ?? '';
        $data['device_id'] = $request->device_id ?? null;
        $data['device_type'] = $request->device_type ?? 0;
        $data['address'] = $request->address ?? null;
        $data['people_id'] = $request->people_id ?? null;
        $data['bind_time'] = Carbon::now();
        $data['deleted'] = $request->deleted ?? 0;
        $db = new BaseDevice();

        $res_sql = $db
            ->where('id', '=', $request->id)
            ->Update($data);

        if (!$res_sql) {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "数据未更新！";
            return $res;
        } else {
            $res['infor'] = "success";
            $res['msg'] = "数据更新成功";
            $res['code'] = 0;
            return $res;
        }
    }

    public function addDeviceInfo(Request $request)
    {
        $enumValues = Cache::remember('device_type_enum_values', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM base_device WHERE Field = 'device_type'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });
        $validator = Validator::make($request->all(), [
            'id' => 'unique:base_device,id|required',
            'name' => 'required|regex:/\p{Han}/u|max:30|min:2',
            'device_id' => [
                "required",
                "min:2",
                Rule::unique('base_device', 'device_id')
            ],
            'device_type' => [
                'required',
                'regex:/\p{Han}/u',
                Rule::in($enumValues),
            ],
            'address' => ["required", "regex:/\p{Han}/u", "max:80", "min:2"],
            'deleted' => ["required", Rule::in(false, true)],
            'people_id' => [
                "required",
                Rule::exists('base_people', 'id')->where(function ($query) {
                    return $query->where('deleted', '=', "0");
                })
            ]
        ], [
                'id.required' => "参数缺失u",
                'id.unique' => "设备ID已存在",
                'deleted.required' => "请选择是否禁用！",
                'deleted.in' => "是否禁用只能为是或否！",
                'name.required' => "设备名称未填写！",
                'name.regex' => "设备名称格式不对，必须为中文！",
                'name.max' => "设备名称长度不得超过30个字哦！",
                'name.min' => "设备名称长度不得低于2个字哦！",
                'device_id.required' => "设备硬件编号未填写！",
                'device_id.min' => "设备硬件编号长度低于2个！",
                'device_id.unique' => "设备硬件编号不能和别的设备重复！",

                'device_type.required' => "请选择设备类型！",
                'device_type.regex' => "设备类型必须是中文！",
                'device_type.in' => "设备类型不是[" . join(',', $enumValues) . "]中的一个！",
                'address.regex' => "设备安装地址必须填写中文！",
                'address.required' => "必须填写设备安装地址！",
                'address.max' => "设备安装地址长度不得超过80个字符！",
                'address.min' => "设备安装地址长度不得低于2个字符！",
                'people_id.required' => "请选择绑定人员！",
                'people_id.exists' => "选择的绑定人员不正确！此人不存在系统中或者已被禁用！",
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $data['name'] = $request->name ?? '';
        $data['id'] = $request->id ?? '';
        $data['device_id'] = $request->device_id ?? null;
        $data['device_type'] = $request->device_type ?? 0;
        $data['address'] = $request->address ?? null;
        $data['people_id'] = $request->people_id ?? null;
        $data['bind_time'] = Carbon::now();
        $data['deleted'] = $request->deleted ?? 0;
        $db = new BaseDevice();

        $res_sql = $db->insert($data);
        if ($res_sql) {
            $res['infor'] = "success";
            $res['msg'] = "保存成功！";
            $res['code'] = 0;
            return $res;
        } else {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "数据添加失败！";
            return $res;
        }
    }

    public function addWarnRuleInfo(Request $request)
    {

        $enumValues = Cache::remember('device_type_enum_values_warn_rule', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM base_device WHERE Field = 'device_type'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });
        $enumValues_decide = Cache::remember('decide_enum_values_warn_rule', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM warn_rule WHERE Field = 'decide'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });
        $validator = Validator::make($request->all(), [
            'id' => 'unique:warn_rule,id|required',
            'name' => 'required|regex:/\p{Han}/u|max:30|min:2',
            'unit' => ["required", "max:10", "min:1"],
            'threshold' => ["required", "numeric"],
            'decide' => [
                'required',
                Rule::in($enumValues_decide),
            ],
            'property' => ["required", "max:10", "min:1"],
            'deleted' => ["required", Rule::in(false, true)],
            'star' => ["required", "numeric", "max:5", "min:1"],
            'device_type' => [
                'required',
                'regex:/\p{Han}/u',
                Rule::in($enumValues),
            ],
        ], [
                'id.required' => "参数缺失u",
                'id.unique' => "预警规则ID已存在",
                'name.required' => "预警规则名称未填写！",
                'name.regex' => "预警规则名称格式不对，必须为中文！",
                'name.max' => "预警规则名称长度不得超过30个字哦！",
                'name.min' => "预警规则名称长度不得低于2个字哦！",
                'unit.required' => "阈值单位未填写！",
                'unit.max' => "阈值单位长度不得超过10个字哦！",
                'unit.min' => "阈值单位长度不得低于1个字哦！",
                'threshold.required' => "阈值未填写！",
                'threshold.numeric' => "阈值必须是数字或者小数！",
                'decide.required' => "请选择判断机制！",
                'decide.in' => "判断机制不是[" . join(',', $enumValues_decide) . "]中的一个！！",

                'property.required' => "英文属性名称未填写！",
                'property.max' => "英文属性名称长度不得超过10个字哦！",
                'property.min' => "英文属性名称长度不得低于1个字哦！",

                'deleted.required' => "请选择是否禁用！",
                'deleted.in' => "是否禁用只能为是或否！",

                'star.required' => "请选择预警星级！",
                'star.max' => "预警星级最大是五星哦！",
                'star.min' => "预警星级最小是一星哦！",
                'star.numeric' => "预警星级必须是数字哦！",

                'device_type.required' => "请选择设备类型！",
                'device_type.regex' => "设备类型必须是中文！",
                'device_type.in' => "设备类型不是[" . join(',', $enumValues) . "]中的一个！",
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $data['name'] = $request->name ?? '';
        $data['id'] = $request->id ?? '';
        $data['unit'] = $request->unit ?? null;
        $data['device_type'] = $request->device_type ?? 0;
        $data['threshold'] = $request->threshold ?? null;
        $data['decide'] = $request->decide ?? null;
        $data['property'] = $request->property ?? null;
        $data['star'] = $request->star ?? null;
        $data['deleted'] = $request->deleted ?? 0;
        $db = new WarnRule();

        $res_sql = $db->insert($data);
        if ($res_sql) {
            $res['infor'] = "success";
            $res['msg'] = "保存成功！";
            $res['code'] = 0;
            return $res;
        } else {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "数据添加失败！";
            return $res;
        }
    }

    public function saveWarnRuleInfo(Request $request)
    {
        $enumValues = Cache::remember('device_type_enum_values_warn_rule', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM base_device WHERE Field = 'device_type'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });
        $enumValues_decide = Cache::remember('decide_enum_values_warn_rule', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM warn_rule WHERE Field = 'decide'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });
        $validator = Validator::make($request->all(), [
            'id' => 'exists:warn_rule,id|required',
            'name' => 'required|regex:/\p{Han}/u|max:30|min:2',
            'unit' => ["required", "max:10", "min:1"],
            'threshold' => ["required", "numeric"],
            'decide' => [
                'required',
                Rule::in($enumValues_decide),
            ],
            'time_span' => ['required', 'numeric', 'min:5', 'max:1440'],
            'push_primise' => [
                'required',
                function ($attribute, $value, $fail) {
                    foreach ($value as $item) {
                        $exists = BasePrimise::where('id', $item)
                            ->where('deleted', 0)
                            ->where('id', '!=', '1')
                            ->where('id', '!=', '5')
                            ->exists();

                        if (!$exists) {
                            $fail("您选择的预警消息推送的人员权限有一个是被禁用了或无效的！");
                        }
                    }
                },
            ],
            'property' => ["required", "max:10", "min:1"],
            'deleted' => ["required", Rule::in(false, true)],
            'star' => ["required", "numeric", "max:5", "min:1"],
            'device_type' => [
                'required',
                'regex:/\p{Han}/u',
                Rule::in($enumValues),
            ],
        ], [

                'time_span.required' => "请输入重复预警类型的推送间隔时间（分钟）",
                'time_span.numeric' => "重复预警类型的推送间隔时间（分钟）必须是数字类型",
                'time_span.max' => "重复预警类型的推送间隔时间（分钟）最大值为1440分钟，即一天",
                'time_span.min' => "重复预警类型的推送间隔时间（分钟）最小值为5分钟",
                'push_primise.required' => "请选择预警消息推送的人员权限列表",
                'id.required' => "参数缺失u",
                'id.exists' => "预警规则ID不存在",
                'name.required' => "预警规则名称未填写！",
                'name.regex' => "预警规则名称格式不对，必须为中文！",
                'name.max' => "预警规则名称长度不得超过30个字哦！",
                'name.min' => "预警规则名称长度不得低于2个字哦！",
                'unit.required' => "阈值单位未填写！",
                'unit.max' => "阈值单位长度不得超过10个字哦！",
                'unit.min' => "阈值单位长度不得低于1个字哦！",
                'threshold.required' => "阈值未填写！",
                'threshold.numeric' => "阈值必须是数字或者小数！",
                'decide.required' => "请选择判断机制！",
                'decide.in' => "判断机制不是[" . join(',', $enumValues_decide) . "]中的一个！！",

                'property.required' => "英文属性名称未填写！",
                'property.max' => "英文属性名称长度不得超过10个字哦！",
                'property.min' => "英文属性名称长度不得低于1个字哦！",

                'deleted.required' => "请选择是否禁用！",
                'deleted.in' => "是否禁用只能为是或否！",

                'star.required' => "请选择预警星级！",
                'star.max' => "预警星级最大是五星哦！",
                'star.min' => "预警星级最小是一星哦！",
                'star.numeric' => "预警星级必须是数字哦！",

                'device_type.required' => "请选择设备类型！",
                'device_type.regex' => "设备类型必须是中文！",
                'device_type.in' => "设备类型不是[" . join(',', $enumValues) . "]中的一个！",
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $data['name'] = $request->name ?? '';
        $data['unit'] = $request->unit ?? null;
        $data['device_type'] = $request->device_type ?? 0;
        $data['threshold'] = $request->threshold ?? null;
        $data['decide'] = $request->decide ?? null;
        $data['property'] = $request->property ?? null;
        $data['star'] = $request->star ?? null;
        $data['deleted'] = $request->deleted ?? 0;
        $data['time_span'] = $request->time_span ?? 0;
        $data['push_primise'] = $request->push_primise ?? null;
        $db = new WarnRule();
        $res_sql = $db
            ->where('id', '=', $request->id)
            ->Update($data);

        if (!$res_sql) {
            $res['code'] = 0;
            $res['infor'] = "error";
            $res['msg'] = "数据未更新！";
            return $res;
        } else {
            $res['infor'] = "success";
            $res['msg'] = "数据更新成功";
            $res['code'] = 0;
            return $res;
        }
    }
}
