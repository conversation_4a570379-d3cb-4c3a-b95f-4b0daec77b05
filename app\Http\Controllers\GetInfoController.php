<?php

namespace App\Http\Controllers;

use App\Models\BaseDevice;
use App\Models\BasePeople;
use App\Models\BasePrimise;
use App\Models\BaseUser;
use App\Models\CacheService;
use App\Models\WarnInfolist;
use App\Models\WarnRule;
use Cache;
use DateTime;
use DateTimeZone;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class GetInfoController extends Controller
{
    public function getDataList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'd' => ['required', 'date'],
            'h' => ['required'],
            'm_start' => ['required', 'date_format:Y-m-d H:i:s'],
            'm_end' => ['required', 'date_format:Y-m-d H:i:s'],
        ], [
            'd.required' => "搜索类型选择有误",
            'd.date' => "日期格式不正确",
            'h.required' => "小时不能为空",
            'm_start.required' => "开始时间不能为空",
            'm_start.date_format' => "开始时间格式不正确",
            'm_end.required' => "结束时间不能为空",
            'm_end.date_format' => "结束时间格式不正确",
        ]);

        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }

        // 假设 $request 是从前端传递过来的请求对象
        $d = $request->input('d'); // 日期，例如 '2024-07-26'
        $h = $request->input('h'); // 小时，例如 '13'
        $m_start = $request->input('m_start'); // 开始时间，例如 '2024-07-30 10:20:00'
        $m_end = $request->input('m_end'); // 结束时间，例如 '2024-07-30 10:30:00'

        // 构建表名
        $tableName = 'device_state_' . str_replace('-', '', substr($d, 0, 7));

        // 构建 SQL 查询
        $sql = "
            SELECT d1.*, p.name
            FROM $tableName d1
            LEFT JOIN base_device d ON d.device_id = d1.device_id
            LEFT JOIN base_people p ON p.id = d.people_id
            WHERE d1.start_time >= ? AND d1.end_time <= ?
            ORDER BY (no_people = 1 OR heart_slow = 1 OR heart_fast = 1 OR breath_slow = 1 OR low_signal = 1) DESC, is_online DESC
        ";

        // DB::connection()->enableQueryLog();
        // 执行 SQL 查询
        $results = DB::select($sql, [$m_start, $m_end]);
        // dd(DB::getQueryLog());

        // 处理查询结果
        $listarr = [];
        foreach ($results as $result) {
            // 处理每一行数据
            $listarr[] = $result;
        }

        $res['msg'] = "数据获取成功";
        $res['code'] = 0;
        $res['list'] = $listarr;
        return $res;
    }
    public function getDeviceDataWithSn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date' => ['required', 'date'],
            'sn' => ['required'],
        ], [
            'date.required' => "日期选择有误",
            'date.date' => "日期格式不正确",
            'sn.required' => "设备ID不能为空",
        ]);

        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }

        // 获取日期和设备ID
        $date = $request->input('date'); // 日期，例如 '2024-07-26'
        $deviceId = $request->input('sn'); // 设备ID，例如 '861821064371609'

        // 创建 DateTime 对象并设置时区为 UTC
        $dateTime = new DateTime($date, new DateTimeZone('UTC'));

        // 将时区转换为上海时间（UTC+8）
        $dateTime->setTimezone(new DateTimeZone('Asia/Shanghai'));
        //dd($dateTime);

        // 将日期格式化为 'Y-m-d' 格式
        $formattedDate = $dateTime->format('Y-m');
        // 构建表名
        $tableName = 'device_state_' . str_replace('-', '', substr($formattedDate, 0, 7));
        // 检查表是否存在
        $exists = DB::connection()->getSchemaBuilder()->hasTable($tableName);

        if (!$exists) {
            // 表不存在
            $res['msg'] = "没有找到对应数据";
            $res['code'] = 0;
            $res['list'] = [];
            return $res;
        }

        $results = DB::table($tableName)->select("start_time", "end_time", "no_people", "is_online", "heart_slow", "heart_fast", "breath_slow", "low_signal")
            ->where("device_id", $deviceId)
            ->get();

        $res['msg'] = "数据获取成功";
        $res['code'] = 0;
        $res['list'] = $results;
        return $res;
    }

    public function getDeviceList(Request $request)
    {
        //dd('error');
        $validator = Validator::make($request->all(), [
            'searchType' => [Rule::in('地址', '姓名', '身份证号', '设备ID', 'signError')],
            'searchKey' => [
                Rule::excludeIf(function () use ($request) {
                    return !isset($request->searchType);
                }),
                'required',
            ],
        ], [
            'searchType.in' => "搜索类型选择有误",
            'searchKey.required' => "请输入搜索数据",
        ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $where = " 1=1";
        $pageTotal = 15;
        if (isset($request->searchType)) {
            switch ($request->searchType) {
                case '地址':
                    $where = 'address like "%' . $request->searchKey . '%"';
                    break;
                case '姓名':
                    $where = 'name like "%' . $request->searchKey . '%"';
                    break;
                case '身份证号':
                    $where = 'idcard like "%' . $request->searchKey . '%"';
                    break;
                case '设备ID':
                    $where = 'device_id like "%' . $request->searchKey . '%"';
                    break;
                case "signError":
                    //$where = 'last_log_time is null';
                    $pageTotal = 10000;
                    break;
                default:
                    # code...
                    break;
            }
        }
        if ($request->user()->TOWN != '') { //非超级管理员
            $where .= "  AND TOWN='" . $request->user()->TOWN . "' ";
        }

        $db = new BaseDevice();
        // DB::connection()->enableQueryLog();
        $paginator = $db->select(
            'base_device.name',
            'base_device.id',
            'base_device.device_id',
            'base_device.device_type',
            'base_device.people_id',
            'base_device.address',
            DB::raw('CASE WHEN base_device.deleted = "0" THEN "正常" ELSE "禁用" END AS deleted'),
            'devlog.last_log_time'
        )
            ->leftJoin(DB::raw('(SELECT DeviceID, MAX(intime) AS last_log_time FROM base_devicelog GROUP BY DeviceID) AS devlog'), 'devlog.DeviceID', '=', 'base_device.DEVICE_ID')

            ->with([
                'user' => function ($query) {
                    $query->select('name', 'id', 'lonlat', 'address_info');
                },
            ])
            ->whereHas('user', function ($query) use ($where) {
                $query->whereRaw($where);

            })
            ->orderBy("create_time", "desc")
            ->get();
        // dd(DB::getQueryLog());
        if ($request->searchKey == 'NoSign') {
            $paginator = $paginator->whereNull("last_log_time")->where("deleted", "=", "正常");
            //dd(DB::getQueryLog());
        } elseif ($request->searchKey == 'Deleted') {
            $paginator = $paginator->where("deleted", "=", "禁用");
            //dd(DB::getQueryLog());
        } elseif ($request->searchKey == 'UnDeleted') {
            $paginator = $paginator->where("deleted", "=", "正常");
            //dd(DB::getQueryLog());
        }

        $temp = $paginator->forPage($request->page, $pageTotal);

        // 重新索引集合
        $reindexedTemp = $temp->values();

        $paginator = new LengthAwarePaginator(
            $reindexedTemp,
            // 当前页的记录
            count($paginator),
            // 所有记录总数
            $pageTotal, // 每页记录数
            $request->page,
            // 当前页码
            ['path' => url()->current()]// 分页链接
        );
        //dd(DB::getQueryLog());
        $res_sql = $paginator->items();

        if (!$res_sql) {
            $res['msg'] = '没有数据';
            $res['infor'] = "error";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $res_sql;
            //dd($res['list']);
            $res['total'] = $paginator->total();
            $res['currentPage'] = $paginator->currentPage();
            $res['nextPageUrl'] = $paginator->nextPageUrl();
            $res['page_size'] = $paginator->perPage();
            return $res;
        }
    }
    public function getWarnRuleList(Request $request)
    {
        if ($request->user()->PRIMISE != 1) { //非超级管理员
            $res['code'] = 101;
            $res['msg'] = "对不起，您不是管理员，没有权限操作！";
            return $res;
        }
        $db = new WarnRule();
        $paginator = $db->select(
            "name",
            "id",
            "unit",
            "threshold",
            "decide",
            "property",
            "device_type",
            "create_time",
            'star',
            'time_span',
            'push_primise',
            DB::raw('case when deleted="0" then "正常" else "禁用" end as deleted')
        )
            ->orderBy("create_time", "desc")
            ->get();
        $temp = $paginator->forPage($request->page, 15);
        $reindexedTemp = $temp->values(); // 重新索引集合

        $paginator = new LengthAwarePaginator(
            $reindexedTemp,
            // 当前页的记录
            count($paginator),
            // 所有记录总数
            15, // 每页记录数
            $request->page,
            // 当前页码
            ['path' => url()->current()]// 分页链接
        );
        $res_sql = $paginator->items();

        if (!$res_sql) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $res_sql;
            $res['total'] = $paginator->total();
            $res['currentPage'] = $paginator->currentPage();
            $res['nextPageUrl'] = $paginator->nextPageUrl();
            $res['page_size'] = $paginator->perPage();
            return $res;
        }
    }
    public function getWarnRuleInfo(Request $request)
    {
        $enumValues = Cache::remember('device_type_enum_values_warn_rule', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM warn_rule WHERE Field = 'device_type'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });
        $enumValues_decide = Cache::remember('decide_enum_values_warn_rule', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM warn_rule WHERE Field = 'decide'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });

        if ($request->id == 0) {
            $res['msg'] = "设备类型加载成功";
            $res['code'] = 0;
            $res['list']['deviceTypeList'] = $enumValues;
            $res['list']['decideList'] = $enumValues_decide;
            $res['list']['primiseList'] = BasePrimise::select('id', 'name')->where('id', '!=', 1)->where('id', '!=', 5)->get();
            $res['list']['deleted'] = false;
            return $res;
        }
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:warn_rule,id',
        ], [
            'id.required' => "参数缺失id",
            'id.exists' => "此预警规则不存在",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $warnRule = WarnRule::select(
            "name",
            "id",
            "unit",
            "threshold",
            "decide",
            "property",
            "device_type",
            "create_time",
            'star',
            "deleted",
            'time_span',
            'push_primise'
        )->find($request->id);

        if (!$warnRule) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $warnRule;
            $res['list']['deviceTypeList'] = $enumValues;
            $res['list']['decideList'] = $enumValues_decide;
            $res['list']['primiseList'] = BasePrimise::select('id', 'name')->where('id', '!=', 1)->where('id', '!=', 5)->get();
            $res['list']['deleted'] = boolval($res['list']['deleted']);
            $res['list']['push_primise'] = json_decode($res['list']['push_primise'], true);
            return $res;
        }
    }

    public function getDeviceInfo(Request $request)
    {
        $enumValues = Cache::remember('device_type_enum_values', 60, function () {
            $columnType = DB::select(DB::raw("SHOW COLUMNS FROM base_device WHERE Field = 'device_type'"))[0]->Type;
            preg_match('/^enum\((.*)\)$/', $columnType, $matches);
            $enumValues = explode(',', $matches[1]);
            return array_map(function ($value) {
                return trim($value, "'");
            }, $enumValues);
        });
        if ($request->id == 0) {
            $res['msg'] = "人员列表、设备类型加载成功";
            $res['code'] = 0;
            $res['list']['peopleList'] = BasePeople::select('id', 'name', 'town', 'big_village')->where('deleted', '=', '0')->get();
            $res['list']['deviceTypeList'] = $enumValues;
            $res['list']['deleted'] = true;
            return $res;
        }
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:base_device,id',
        ], [
            'id.required' => "参数缺失id",
            'id.exists' => "此设备不存在",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        //DB::connection()->enableQueryLog();
        $device = BaseDevice::select(
            'id',
            'name',
            'device_id',
            'device_type',
            'address',
            'deleted',
            'people_id',
            DB::raw('case when bind_time is null then "未绑定" else bind_time end as bind_time'),
        )->with([
            'user' => function ($query) {
                $query->select('id', DB::raw('case when id is null then "未绑定" else name end as bind_people'), );
            },
            'warn_infolist',
            'devicelog',
        ])
            ->find($request->id);
        //dd(DB::getQueryLog());

        if (!$device) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $device;
            $res['list']['peopleList'] = BasePeople::select('id', 'name', 'town', 'big_village')->where('deleted', '=', '0')->get();
            $res['list']['deviceTypeList'] = $enumValues;
            $res['list']['deleted'] = boolval($res['list']['deleted']);
            return $res;
        }
    }

    public function getOldmanList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'searchType' => [Rule::in('地址', '姓名', '身份证号', '设备ID')],
            'searchKey' => [
                Rule::excludeIf(function () use ($request) {
                    return !isset($request->searchType);
                }),
                'required',
            ],
        ], [
            'searchType.in' => "搜索类型选择有误",
            'searchKey.required' => "请输入搜索数据",
        ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $where = " 1=1";

        if (isset($request->searchType)) {
            switch ($request->searchType) {
                case '地址':
                    $where = 'address like "%' . $request->searchKey . '%"';
                    break;
                case '姓名':
                    $where = 'name like "%' . $request->searchKey . '%"';
                    break;
                case '身份证号':
                    $where = 'idcard like "%' . $request->searchKey . '%"';
                    break;
                case '设备ID':
                    $where = 'device_id like "%' . $request->searchKey . '%"';
                    break;

                default:
                    # code...
                    break;
            }
        }
        if ($request->user()->TOWN != '') { //非超级管理员
            $where_town = "  TOWN='" . $request->user()->TOWN . "' ";
        } else {
            $where_town = " 1=1 ";
        }

        $db = new BasePeople();
        $paginator = $db->select(
            "name",
            "id",
            "town",
            "lonlat",
            "big_village",
            "phone",
            "address",
            "address_info",
            DB::raw('case when deleted="0" then "正常" else "禁用" end as deleted')
        )
            ->with(['files'])
            ->whereRaw($where)
            ->whereRaw($where_town)
            ->orderBy("town", "desc")
            ->orderBy("big_village", "desc")
            ->get();
        $temp = $paginator->forPage($request->page, 15);
        $reindexedTemp = $temp->values(); // 重新索引集合

        $paginator = new LengthAwarePaginator(
            $reindexedTemp,
            // 当前页的记录
            count($paginator),
            // 所有记录总数
            15, // 每页记录数
            $request->page,
            // 当前页码
            ['path' => url()->current()]// 分页链接
        );
        $res_sql = $paginator->items();
        if (!$res_sql) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $res_sql;
            $res['total'] = $paginator->total();
            //dd($paginator->getPageName());
            $res['currentPage'] = $paginator->currentPage();
            $res['nextPageUrl'] = $paginator->nextPageUrl();
            $res['page_size'] = $paginator->perPage();
            return $res;
        }
    }

    public function getOldmanInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:base_people,id',
        ], [
            'id.required' => "参数缺失id",
            'id.exists' => "此人员不存在",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $order = BasePeople::select(
            'name',
            'idcard',
            'address',
            'town',
            'big_village',
            'phone',
            'relation_name',
            'relation_phone',
            'cadre_name',
            'cadre_phone',
            'deleted',
            DB::raw('case when address_info is null then "未定位" else address_info end as address_info'),
            DB::raw('case when collect_time is null then "未采集" else collect_time end as collect_time'),
        )->with(['files'])->find($request->id);

        if (!$order) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $order;
            $res['list']['deleted'] = boolval($res['list']['deleted']);
            return $res;
        }
    }

    public function getUserList(Request $request)
    {
        if ($request->user()->PRIMISE != 1) { //非超级管理员
            $res['code'] = 101;
            $res['msg'] = "对不起，您不是管理员，没有权限操作！";
            return $res;
        }
        $db = new BaseUser();
        $paginator = $db->select(
            "name",
            "id",
            "town",
            "username",
            "phone",
            "primise",
            DB::raw('case when deleted="0" then "正常" else "禁用" end as deleted')
        )
            ->with([
                'primise' => function ($query) {
                    $query->select('id', 'name');
                },
            ])
            ->orderBy("primise", "asc")
            ->get();
        $temp = $paginator->forPage($request->page, 15);
        $reindexedTemp = $temp->values(); // 重新索引集合

        $paginator = new LengthAwarePaginator(
            $reindexedTemp,
            // 当前页的记录
            count($paginator),
            // 所有记录总数
            15, // 每页记录数
            $request->page,
            // 当前页码
            ['path' => url()->current()]// 分页链接
        );
        $res_sql = $paginator->items();

        if (!$res_sql) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $res_sql;
            $res['total'] = $paginator->total();
            $res['currentPage'] = $paginator->currentPage();
            $res['nextPageUrl'] = $paginator->nextPageUrl();
            $res['page_size'] = $paginator->perPage();
            return $res;
        }
    }

    public function getUserInfo(Request $request)
    {
        if ($request->id == 0) {
            $res['msg'] = "角色列表加载成功";
            $res['code'] = 0;
            $res['list']['primiseList'] = BasePrimise::select('id', 'name')->where('id', '!=', 1)->get();
            $res['list']['townList'] = BasePeople::select('town')->where('deleted', '=', 0)->groupBy('town')->get();
            $res['list']['deleted'] = false;
            return $res;
        }
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:base_users,id',
        ], [
            'id.required' => "参数缺失id",
            'id.exists' => "此用户不存在",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $order = BaseUser::select(
            'name',
            'username',
            'town',
            'wx_id',
            'phone',
            'openid',
            'deleted',
            'updata_time',
            'primise',
            'id'
        )->find($request->id);
        if (!$order) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $order;
            $res['list']['deleted'] = boolval($res['list']['deleted']);
            $res['list']['primiseList'] = BasePrimise::select('id', 'name')->where('id', '!=', 1)->get();
            $res['list']['townList'] = BasePeople::select('town')->where('deleted', '=', 0)->groupBy('town')->get();
            return $res;
        }
    }

    public function getWarnInfoTj(Request $request)
    {
        $data = CacheService::getCachedTjData($request);
        return $data;
        // DB::connection()->enableQueryLog();
        // $db = new BasePeople();
        // if ($request->user()->TOWN != '') { //非超级管理员
        //     $where_town = "  TOWN='" . $request->user()->TOWN . "' ";
        //     $is_admin=false;
        // } else {
        //     $where_town = " 1=1 ";
        //     $is_admin=true;
        // }
        // $paginator = $db->select(
        //     "id",
        //     "name",
        //     "address",
        //     "lonlat as zb"
        // )
        //     ->with(['files'])
        //     ->withCount([
        //         'warn_infolist as dcz' => function ($query) {
        //             $query->where('handle_state', '=', '待处置');
        //         },
        //         'warn_infolist as ycz' => function ($query) {
        //             $query->where('handle_state', '=', '已处置');
        //         },
        //         'warn_infolist as total',
        //         'warn_infolist as value'
        //     ])
        //     ->where("deleted", "=", "0")
        //     ->whereRaw("lonlat is not null")
        //     ->whereRaw($where_town)
        //     ->get();
        // // dd(DB::getQueryLog());
        // $res_sql = $paginator;

        // //已处置未处置记录数
        // $czTj = WarnInfolist::select('handle_state', DB::raw('count(1) as num'))
        // ->when(!$is_admin, function ($query) use ($where_town) {
        //     // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
        //     return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
        //         ->whereRaw($where_town);
        // })
        //     ->groupBy('handle_state')
        //     ->get();
        // //dd($czTj);
        // // 初始化两个数组，用于存放待处置和已处置的结果
        // $pendingArray = ['handle_state' => '待处置', 'num' => 0];
        // $handledArray = ['handle_state' => '已处置', 'num' => 0];

        // // 遍历查询结果，将结果放入相应的数组中
        // foreach ($czTj as $result) {
        //     //dd($result->handle_state,$result->num);
        //     if($result->handle_state == '待处置'){
        //         $pendingArray['num'] = $result->num;
        //     }else{
        //         $handledArray['num'] = $result->num;
        //     }
        // }

        // // 将两个数组按照 "待处置" 和 "已处置" 的顺序排序
        // $sortedResult = [$pendingArray, $handledArray];
        // //dd($sortedResult);
        // //已处置的预警分类
        // $yczFl = WarnInfolist::select('warn_title as name', DB::raw('count(1) as value'))
        // ->when(!$is_admin, function ($query) use ($where_town) {
        //     // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
        //     return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
        //         ->whereRaw($where_town);
        // })
        //     ->where('handle_state', '已处置')
        //     ->groupBy('warn_title')
        //     ->get();
        // //当月预警数
        // $dyYjNum = WarnInfolist::select('warn_infolist.id','warn_infolist.people_id')
        // ->when(!$is_admin, function ($query) use ($where_town) {
        //     // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
        //     return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
        //         ->whereRaw($where_town);
        // })
        //     ->whereRaw('MONTH(warn_time) = MONTH(NOW())')
        //     ->count();
        // //每个月的预警数
        // $every_month = WarnInfolist::select(DB::raw('MONTH(warn_time) as month'), DB::raw('COUNT(warn_infolist.id) as num'))
        // ->when(!$is_admin, function ($query) use ($where_town) {
        //     // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
        //     return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
        //         ->whereRaw($where_town);
        // })
        //     ->whereYear('warn_time', '=', now()->year)
        //     ->groupBy(DB::raw('MONTH(warn_time)'))
        //     ->get();
        // //每个月的已处置预警数
        // $every_month_ycz = WarnInfolist::select(DB::raw('MONTH(warn_time) as month'), DB::raw('COUNT(warn_infolist.id) as num'))
        // ->when(!$is_admin, function ($query) use ($where_town) {
        //     // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
        //     return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
        //         ->whereRaw($where_town);
        // })
        //     ->whereYear('warn_time', '=', now()->year)
        //     ->where('handle_state', '已处置')
        //     ->groupBy(DB::raw('MONTH(warn_time)'))
        //     ->get();
        // //特困人员数
        // $tkrrNum = BaseDevice::select('id')
        // ->whereRaw($where_town)
        //     ->where('deleted', 0)
        //     ->count();
        // //各预警分类数
        // $yjFl = WarnInfolist::select('warn_title', DB::raw('count(1) as num'))
        // ->when(!$is_admin, function ($query) use ($where_town) {
        //     // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
        //     return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
        //         ->whereRaw($where_town);
        // })
        //     ->groupBy('warn_title')
        //     ->get();

        // if (!$res_sql) {
        //     $res['code'] = 101;
        //     $res['msg'] = "没有数据！";
        //     return $res;
        // } else {
        //     $res['msg'] = "数据获取成功";
        //     $res['code'] = 0;
        //     $res['every_month'] = $every_month;
        //     $res['every_month_ycz'] = $every_month_ycz;
        //     $res['list'] = $res_sql;
        //     $res['czTj'] = $sortedResult;
        //     $res['yjFl'] = $yjFl;
        //     $res['dyYjNum'] = $dyYjNum;
        //     $res['tkrrNum'] = $tkrrNum;
        //     $res['yczFl'] = $yczFl;
        //     $res['limit'] = $res_sql->filter(function ($value, $key) {
        //         return $value->value > 0;
        //     })->take(10)->values()->all();
        //     return $res;
        // }
    }
    public function getWarnInfoList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'searchType' => [Rule::in('触发人员', '预警标题', '处置状态')],
            'searchKey' => [
                Rule::excludeIf(function () use ($request) {
                    return !isset($request->searchType);
                }),
                'required',
            ],
        ], [
            'searchType.in' => "搜索类型选择有误",
            'searchKey.required' => "请输入搜索数据",
        ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $where = " 1=1";

        if (isset($request->searchType)) {
            switch ($request->searchType) {
                case '触发人员':
                    $where = 'name = "' . $request->searchKey . '"';
                    break;
                case '预警标题':
                    $where = 'warn_title ="' . $request->searchKey . '"';
                    break;
                case '处置状态':
                    $where = 'handle_state ="' . $request->searchKey . '"';
                    break;
                default:
                    $where = 'deleted =1';
                    # code...
                    break;
            }
        }
        if (is_array($request->start_to_end) && count($request->start_to_end) == 2) {
            $where .= ' and ((warn_time)>\'' . ($request->start_to_end[0]) . '\' and (warn_time)<\'' . ($request->start_to_end[1]) . '\')';
        }
        if ($request->user()->TOWN != '') { //非超级管理员
            $where .= "  AND TOWN='" . $request->user()->TOWN . "' ";
        }
        //DB::connection()->enableQueryLog();
        $db = new WarnInfolist();

        $totalCount = $db->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
            ->where('warn_infolist.deleted', '=', '0')
        // ->where('warn_title', '!=', '设备离线预警')
            ->whereRaw($where)
            ->count();
        //dd(DB::getQueryLog());
        $pageSize = 15;
        $page = $request->page;
        $totalPages = ceil($totalCount / $pageSize);

        //DB::connection()->enableQueryLog();
        $paginator = $db->select(
            "warn_infolist.id",
            "warn_infolist.warn_title",
            "warn_infolist.warn_decription",
            "warn_infolist.last_set_time",
            "warn_infolist.people_id",
            "warn_infolist.handle_state",
            "warn_infolist.handle_text",
            "warn_infolist.handle_name",
            "warn_infolist.warn_time",
            "warn_infolist.handle_time"
        )
            ->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
            ->select('warn_infolist.*', 'base_people.name', 'base_people.address', 'base_people.lonlat')
            ->where("warn_infolist.deleted", "=", "0")
        // ->where('warn_title', '!=', '设备离线预警')
            ->whereRaw($where)
            ->orderBy("warn_infolist.warn_time", "desc")
            ->skip(($page - 1) * $pageSize)
            ->take($pageSize)
            ->get();
        //dd(DB::getQueryLog());
        if (!$paginator) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $paginator;
            $res['total'] = $totalCount;
            $res['currentPage'] = $request->page;
            $res['nextPageUrl'] = "";
            $res['page_size'] = 15;
            return $res;
        }
    }
    public function getWarnCategoryInfoList(Request $request)
    {
        // if (is_array($request->start_to_end) && count($request->start_to_end) == 2) {
        //     $where .= ' and ((warn_time)>\'' . ($request->start_to_end[0]) . '\' and (warn_time)<\'' . ($request->start_to_end[1]) . '\')';
        // }
        // if ($request->user()->TOWN != '') { //非超级管理员
        //     $where .= "  AND TOWN='" . $request->user()->TOWN . "' ";
        // }
        // // DB::connection()->enableQueryLog();
        // $db = new WarnInfolist();

        // $totalCount = $db->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
        //     ->where('warn_infolist.deleted', '=', '0')
        //     ->whereRaw($where)
        //     ->count();

        // $pageSize = 15;
        // $page = $request->page;
        // $totalPages = ceil($totalCount / $pageSize);

        // DB::connection()->enableQueryLog();
        $where = "";
        if (is_array($request->start_to_end) && count($request->start_to_end) == 2) {
            $where .= ' where ((warn_time)>=\'' . ($request->start_to_end[0]) . '\' and (warn_time)<\'' . ($request->start_to_end[1]) . '\')';
        }
        $paginator = DB::select("SELECT
        warn_title,
        SUM(nums) AS warn_sum,
        CONCAT(ROUND(SUM(nums) * 100.0 / (SELECT COUNT(*) FROM warn_infolist " . $where . "),2),'%') AS warn_sun_per,
        SUM(CASE WHEN handle_state = '已处置' THEN nums ELSE 0 END) AS warn_yichuzhi,
        CONCAT(ROUND(SUM(CASE WHEN handle_state = '已处置' THEN nums ELSE 0 END) * 100.0 / SUM(nums),2),'%') AS warn_yichuzhi_per,
        SUM(CASE WHEN handle_state = '待处置' THEN nums ELSE 0 END) AS warn_daichuzhi,
        CONCAT(ROUND(SUM(CASE WHEN handle_state = '待处置' THEN nums ELSE 0 END) * 100.0 / SUM(nums),2),'%') AS warn_daichuzhi_zhanbi
    FROM (
        SELECT
            warn_title,
            handle_state,
            COUNT(*) AS nums
        FROM
            warn_infolist
" . $where . "
        GROUP BY
            warn_title, handle_state
    ) AS subquery
    GROUP BY
        warn_title
    ORDER BY
        warn_title DESC;");
        // dd(DB::getQueryLog());
        if (!$paginator) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $paginator;
            return $res;
        }
    }
    public function getWarnInfoListToXlsx(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'searchType' => [Rule::in('触发人员', '预警标题')],
            'searchKey' => [
                Rule::excludeIf(function () use ($request) {
                    return !isset($request->searchType);
                }),
                'required',
            ],
        ], [
            'searchType.in' => "搜索类型选择有误",
            'searchKey.required' => "请输入搜索数据",
        ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        $where = " 1=1";

        if (isset($request->searchType)) {
            switch ($request->searchType) {
                case '触发人员':
                    $where = 'name = "' . $request->searchKey . '"';
                    break;
                case '预警标题':
                    $where = 'warn_title ="' . $request->searchKey . '"';
                    break;
                default:
                    # code...
                    break;
            }
        }
        if (is_array($request->start_to_end) && count($request->start_to_end) == 2) {
            $where .= ' and ((warn_time)>\'' . ($request->start_to_end[0]) . '\' and (warn_time)<\'' . ($request->start_to_end[1]) . '\')';
        }
        if ($request->user()->TOWN != '') { //非超级管理员
            $where .= "  AND TOWN='" . $request->user()->TOWN . "' ";
        }

        $db = new WarnInfolist();

        // $totalCount = $db->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
        //     ->where('warn_infolist.deleted', '=', '0')
        //     ->whereRaw($where)
        //     ->count();

        // $pageSize = 15;
        $page = $request->page;
        // $totalPages = ceil($totalCount / $pageSize);

        // DB::connection()->enableQueryLog();
        $paginator = $db->select(
            "warn_infolist.id",
            "warn_infolist.warn_title",
            "warn_infolist.warn_decription",
            "warn_infolist.last_set_time",
            "warn_infolist.people_id",
            "warn_infolist.handle_state",
            "warn_infolist.handle_text",
            "warn_infolist.handle_name",
            "warn_infolist.warn_time",
            "warn_infolist.handle_time"
        )
            ->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
            ->select('warn_infolist.*', 'base_people.name', 'base_people.address', 'base_people.lonlat')
            ->where("warn_infolist.deleted", "=", "0")
            ->whereRaw($where)
            ->orderBy("warn_infolist.warn_time", "desc")
            ->skip(($page - 1) * $pageSize)
            ->take($pageSize)
            ->get();

        if (!$paginator) {
            $res['code'] = 101;
            $res['msg'] = "没有数据！";
            return $res;
        } else {
            $res['msg'] = "数据获取成功";
            $res['code'] = 0;
            $res['list'] = $paginator;
            $res['total'] = $totalCount;
            $res['currentPage'] = $request->page;
            $res['nextPageUrl'] = "";
            $res['page_size'] = 15;
            return $res;
        }
    }
}
