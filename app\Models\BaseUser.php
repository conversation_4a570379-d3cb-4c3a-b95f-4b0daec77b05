<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class BaseUser extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $table = 'base_users';
    protected $guarded = ['id', 'create_time', 'updata_time'];
    protected $dates = ['create_time', 'updata_time'];
    public $incrementing = false;
    protected $primaryKey = 'id';
    public $timestamps = false;
    protected $keyType = 'string';

    protected $fillable = [
        'password',
        'name',
        'primise',
        'town',
        'deleted',
        'wx_id',
        'phone',
        'openid',
    ];

    protected $hidden = [
        'password',
        'openid'
    ];

    protected $casts = [
        'id' => 'string',
        'primise' => 'integer'
    ];

    public function selectFromWhere($username, $password)
    {
        $res = $this->select('id as uuid', 'id', 'town', 'username', 'name', 'phone', 'primise')
            ->where('username', '=', $username)
            ->where('password', '=', md5($password))
            ->where(function ($query) {
                $query->where('primise', '=', 1)
                    ->orWhere('primise', '=', 5)
                    ->orWhere('primise', '=', 6);
            })
            ->where('deleted', '=', 0)
            ->first();
        return $res;
    }
    public function primise()
    {
        return $this->belongsTo(BasePrimise::class, 'primise');
    }
}
