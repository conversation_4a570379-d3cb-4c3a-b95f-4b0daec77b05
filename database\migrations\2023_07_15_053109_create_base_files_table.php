<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBaseFilesTable extends Migration
{
    public function up()
    {
        Schema::create('base_files', function (Blueprint $table) {
            $table->id();
            $table->string('tablename', 255)->nullable()->comment('表名称');
            $table->unsignedBigInteger('father_id')->nullable()->comment('父表主键ID');
            $table->string('path', 255)->nullable()->comment('文件路径');
            $table->string('f_type', 255)->nullable()->comment('文件类型');
            $table->unsignedBigInteger('createby')->nullable()->comment('添加者');
            $table->timestamp('CREATE_TIME')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('添加时间');
            $table->unsignedBigInteger('t')->nullable()->comment('第几张');
            $table->timestamp('UPDATA_TIME')->nullable()->default(null)->useCurrentOnUpdate()->comment('更新时间');
            $table->timestamps();
            $table->index('father_id');
            $table->index('f_type');
            $table->index('tablename');
            $table->index('createby');
            $table->index('t');
        });
    }

    public function down()
    {
        Schema::dropIfExists('base_files');
    }
}
