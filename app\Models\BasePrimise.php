<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BasePrimise extends Model
{
    use HasFactory;

    protected $table = 'base_primise';
    protected $primaryKey = 'id';
    public $incrementing = true;
    protected $guarded = ['id', 'create_time', 'updata_time'];
    protected $dates = ['create_time', 'updata_time'];
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'updata_time';

    protected $fillable = [
        'name',
        'level',
        'deleted',
    ];

    protected $casts = [
        'level' => 'integer',
        'deleted' => 'boolean',
        'id'=>'integer'
    ];
}
