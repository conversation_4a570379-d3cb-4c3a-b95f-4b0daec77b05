<?php

use App\Http\Controllers\SaveInfoController;
use App\Http\Controllers\UploadController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\GetInfoController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
Route::get('/error', function () {
    $list['msg'] = "登录信息已失效，请重新登录";
    $list['errorCode'] = 401;
    $list['infor'] = "登录信息已失效，请重新登录";
    return $list;
})->name("login");
Route::controller(LoginController::class)->prefix('login')->group(function () {
    Route::post('/doLogin', 'doLogin');
});
//每个客户端IP每分钟最大请求30次 index路由
//->middleware('throttle:120:1')
Route::middleware(['auth:sanctum','ability:admin:all,admin:user'])->group(function () {
    //所有用户均可退出系统
    Route::controller(LoginController::class)->prefix('login')->group(function () {
        Route::post('/logOut', 'logOut');
    });

    Route::controller(GetInfoController::class)->prefix('getinfo')->group(function () {
        //所有用户均可获取设备列表
        Route::post('/getDeviceList', 'getDeviceList');
        //所有用户均可获取设备信息
        Route::post('/getDeviceInfo', 'getDeviceInfo');
        //所有用户均可获取人员信息
        Route::post('/getOldmanInfo', 'getOldmanInfo');
        //所有用户均可获取人员列表
        Route::any('/getOldmanList', 'getOldmanList');
        // Route::post('/getUserList', 'getUserList');
        // Route::post('/getUserInfo', 'getUserInfo');
        // Route::post('/getWarnRuleList', 'getWarnRuleList');
        // Route::post('/getWarnRuleInfo', 'getWarnRuleInfo');
        Route::post('/getWarnInfoTj', 'getWarnInfoTj');
        Route::post('/getWarnInfoList', 'getWarnInfoList');
        Route::post('/getWarnCategoryInfoList', 'getWarnCategoryInfoList');

        Route::post('/getDataList', 'getDataList');
        Route::post('/getDeviceDataWithSn', 'getDeviceDataWithSn');
    });
});

Route::middleware(['auth:sanctum','ability:admin:all'])->group(function () {
    Route::controller(GetInfoController::class)->prefix('getinfo')->group(function () {
        Route::post('/getUserList', 'getUserList');
        Route::post('/getUserInfo', 'getUserInfo');
        Route::post('/getWarnRuleList', 'getWarnRuleList');
        Route::post('/getWarnRuleInfo', 'getWarnRuleInfo');
    });
    Route::controller(SaveInfoController::class)->prefix('saveinfo')->group(function () {
        Route::post('/saveUserInfo', 'saveUserInfo');
        Route::post('/saveOldmanInfo', 'saveOldmanInfo');
        Route::post('/addOldmanInfo', 'addOldmanInfo');
        Route::post('/saveDeviceInfo', 'saveDeviceInfo');
        Route::post('/addDeviceInfo', 'addDeviceInfo');
        Route::post('/saveUserInfo', 'saveUserInfo');
        Route::post('/addUserInfo', 'addUserInfo');
        Route::post('/saveWarnRuleInfo', 'saveWarnRuleInfo');
        Route::post('/addWarnRuleInfo', 'addWarnRuleInfo');
    });
    Route::controller(UploadController::class)->prefix('upload')->group(function () {
        Route::post('/importExcel', 'importExcel');
        Route::post('/save', 'save');
    });
});
