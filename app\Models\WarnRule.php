<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WarnRule extends Model
{
    use HasFactory;

    protected $table = 'warn_rule';

    protected $primaryKey = 'ID';
    public $timestamps = false;
    protected $keyType = 'string';

    protected $fillable = [
        'ID',
        'NAME',
        'UNIT',
        'THRESHOLD',
        'DECIDE',
        'PROPERTY',
        'CREATE_TIME',
        'UPDATA_TIME',
        'DELETED',
        'STAR',
        'DEVICE_TYPE',
        'TIME_SPAN',
        'PUSH_PRIMISE'
    ];
    protected $casts = [
        'PUSH_PRIMISE' => 'array',
    ];

    // 其他属性和方法
}
