<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WarnInfolist extends Model
{
    use HasFactory;
/**
 * 预警表，非推送表，此处生成warnid
 */
    protected $table = 'warn_infolist';

    protected $primaryKey = 'id';
    public $timestamps = false;

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'to_user_id',
        'warn_title',
        'warn_decription',
        'last_set_time',
        'people_id',
        'device_id',
        'handle_state',
        'handle_text',
        'handle_userid',
        'handle_name',
        'handle_time',
        'create_time',
        'updata_time',
        'deleted',
        'warn_time',
        'warn_rule_id'
    ];
    public function device()
    {
        return $this->belongsTo(BaseDevice::class, 'device_id');
    }

    public function people()
    {
        return $this->belongsTo(BasePeople::class, 'people_id');
    }

    public function user()
    {
        return $this->belongsTo(BaseUser::class, 'to_user_id');
    }
}
