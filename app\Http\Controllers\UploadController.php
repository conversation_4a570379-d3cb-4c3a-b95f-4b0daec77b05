<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\BasePeople;
use Rap2hpoutre\FastExcel\FastExcel;
use Illuminate\Support\Facades\Storage;
use DB;
use Cache;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class UploadController extends Controller
{
    //
    public function importExcel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required',
        ], [
                'file.required' => "没有文件！",
            ]);
        if ($validator->fails()) {
            $res['msg'] = $validator->errors()->first();
            $res['infor'] = "error";
            return $res;
        }
        if ($request->file->extension() != 'xlsx') {
            $res['msg'] = "只支持xlsx文件！请您不要直接改后缀，而是使用WPS或Office另存为xlsx！";
            $res['infor'] = "error";
            return $res;
        }
        $path = $request->file->store('temp');
        $fullPath = Storage::path($path);
        //dd(Storage::path($tmp));
        $collection = (new FastExcel)->import($fullPath);
        if (count($collection) < 5) {
            $res['msg'] = "表格不足五行，取消导入！";
            $res['infor'] = "error";
            return $res;
        }
        if (!isset($collection[0]['全自理分散特困人员姓名'])) {
            $res['msg'] = "第一行必须是含有【全自理分散特困人员姓名】的表头，请删除无用的标题！";
            $res['infor'] = "error";
            return $res;
        }
        if (!isset($collection[0]['身份证号'])) {
            $res['msg'] = "第一行必须是含有【身份证号】的表头，请删除无用的标题！";
            $res['infor'] = "error";
            return $res;
        }
        if (!isset($collection[0]['镇街'])) {
            $res['msg'] = "第一行必须是含有【镇街】的表头，请删除无用的标题！";
            $res['infor'] = "error";
            return $res;
        }
        if (!isset($collection[0]['村、社区'])) {
            $res['msg'] = "第一行必须是含有【村、社区】的表头，请删除无用的标题！";
            $res['infor'] = "error";
            return $res;
        }
        if (!isset($collection[0]['住址'])) {
            $res['msg'] = "第一行必须是含有【住址】的表头，请删除无用的标题！";
            $res['infor'] = "error";
            return $res;
        }
        if (!isset($collection[0]['核实最新情况（居住情况有变化或已死亡的也请备注）'])) {
            $res['msg'] = "第一行必须是含有【核实最新情况（居住情况有变化或已死亡的也请备注）】的表头，请删除无用的标题！";
            $res['infor'] = "error";
            return $res;
        }

        $oldmanTable = Cache::remember('oldman_table', 5, function () {
            return BasePeople::select("idcard", "town", "big_village", "address", "name","beizhu")->get();
        });
        $insertData = []; //新增
        $upData = []; //变更或取消资格
        $delData = []; //变更或取消资格
        foreach ($collection as $newkey => $newvalue) {
            //循环新表，找到新增和变更
            $found = $oldmanTable->where('idcard', $newvalue['身份证号'])->first();
            if ($found) {
                //判断是否有变更
                //$upData[]=['name' => 'John', 'age' => 25];
                if (
                    $newvalue['全自理分散特困人员姓名'] != $found['name'] || $newvalue['身份证号'] != $found['idcard'] || $newvalue['镇街'] != $found['town']
                    || $newvalue['村、社区'] != $found['big_village'] || $newvalue['住址'] != $found['address']
                    || $newvalue['核实最新情况（居住情况有变化或已死亡的也请备注）'] != $found['beizhu']
                ) {
                    $upData[] = [
                        'id'=>Str::uuid(),
                        'name' => $newvalue['全自理分散特困人员姓名'],
                        'idcard' => $newvalue['身份证号'],
                        'town' => $newvalue['镇街'],
                        'big_village' => $newvalue['村、社区'],
                        'address' => $newvalue['住址'],
                        'beizhu' => $newvalue['核实最新情况（居住情况有变化或已死亡的也请备注）']
                    ];
                }

            } else {
                //需要插入
                $insertData[] = [
                    'name' => $newvalue['全自理分散特困人员姓名'],
                    'id'=>Str::uuid(),
                    'idcard' => $newvalue['身份证号'],
                    'town' => $newvalue['镇街'],
                    'big_village' => $newvalue['村、社区'],
                    'address' => $newvalue['住址'],
                    'beizhu' => $newvalue['核实最新情况（居住情况有变化或已死亡的也请备注）']
                ];
            }

        }
        foreach ($oldmanTable as $oldkey => $oldvalue) {
            //循环旧表，找到取消资格
            $found = $collection->where('身份证号', $oldvalue['idcard'])->isNotEmpty();
            if (!$found) {
                //此人取消资格
                $delData[] = [
                    'name' => $oldvalue['name'],
                    'id'=>Str::uuid(),
                    'idcard' => $oldvalue['idcard'],
                    'town' => $oldvalue['town'],
                    'big_village' => $oldvalue['big_village'],
                    'address' => $oldvalue['address'],
                    'deleted' => 1
                ];
            }
        }
        //dd(count($oldmanTable),count($collection));
        //生成两个数组供确定处调用
        Cache::forget('importExcel');
        $oldmanTable = Cache::remember('importExcel', 600,function () use ($insertData, $upData, $delData) {
            return ['insert' => $insertData, 'update' => $upData, 'delete' => $delData];
        });
        if (count($oldmanTable['insert']) > 0 || count($oldmanTable['update']) > 0 || count($oldmanTable['delete']) > 0) {
            $res['code'] = 0;
            $res['infor'] = "confirm";
            $res['list'] = $oldmanTable;
            $res['msg'] = "请确认数据更新情况！";
            return $res;
        } else {
            $res['infor'] = "error";
            $res['msg'] = "数据未有变化！";
            $res['code'] = 0;
            return $res;
        }
    }

    public function save(Request $request){
        $cacheData=Cache::get('importExcel');
        if($cacheData){
            $insert=BasePeople::insertOrIgnore($cacheData['insert']);
            $update=BasePeople::upsert($cacheData['update'],['idcard'], ['address','name','town','big_village','beizhu']);
            $delete=BasePeople::upsert($cacheData['delete'],['idcard'], ['deleted']);
            $res['infor'] = "success";
            $res['msg'] = "更新完成！新增".$insert."条，更新".$update."条，取消资格".$delete."条,请刷新获取最新数据！";
            $res['code'] = 0;
            Cache::forget('importExcel');
            return $res;
        }
        else{
            $res['infor'] = "error";
            $res['msg'] = "已更新或超时了！";
            $res['code'] = 0;
            return $res;
        }
    }
}
