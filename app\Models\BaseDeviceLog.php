<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BaseDeviceLog extends Model
{
    protected $table = 'base_devicelog';
    protected $primaryKey = 'ID';
    public $timestamps = false;

    // Specify the fillable columns
    protected $fillable = [
        'DeviceID',
        'RoomBM',
        'EnvSound',
        'EnvVoice',
        'GradeSound',
        'SoundMarkTime',
        'HeartRate',
        'BreathRate',
        'LightLx',
        'GradeLight',
        'RangeLxChange',
        'HasPeople',
        'HasSound',
        'HasMov',
        'HasLx',
        'HasLight',
        'Temperature',
        'Humidity',
        'InTime',
        'Smoking',
    ];

    public function device()
    {
        return $this->belongsTo(BaseDevice::class, 'DeviceID');
    }

    // Define any relationships or additional methods here
}
