<?php

namespace App\Http\Controllers;

use App\Models\BaseUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    //登录接口
    public function doLogin(Request $request)
    {
        //dd($request->cookies);
        $validator = Validator::make($request->all(), [
            'username' => 'required|max:16|min:2',
            'password' => 'required',
        ], [
                'username.required' => "请输入用户名",
                'username.max' => "用户名最大长度不能超过16个字符",
                'username.min' => "用户名最小长度不能低于5个字符",
                'password.required' => "请输入密码",
            ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }


        $db = new BaseUser();
        $res_sql = $db->selectFromWhere($request->username, $request->password);
        if (!$res_sql) {
            $res['msg'] = 1;
            $res['infor'] = "用户名密码不对！";
            return $res;
        } else {
            $res['msg'] = 2;
            $res['infor'] = "登录成功！";
            $res['list'] = $res_sql;
            if($res_sql->primise==1){
                $res['token'] = $res_sql->createToken('fsgy_admin', ['admin:all'])->plainTextToken;
            }else{
                $res['token'] = $res_sql->createToken('fsgy_admin', ['admin:user'])->plainTextToken;
            }

            return $res;
        }
    }
}
