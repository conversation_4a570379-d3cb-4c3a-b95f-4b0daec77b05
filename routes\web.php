<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\GetInfoController;
use App\Http\Controllers\SaveInfoController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/', function () {
//     return view('welcome');
// });


// Route::get('/index', function () {
//     return view('index');
// })->name('index');

// Route::get('/api/error', function () {
//     $list['msg'] = "登录信息已失效，请重新登录";
//     $list['errorCode'] = 401;
//     $list['code'] = 401;
//     $list['infor'] = "登录信息已失效，请重新登录";
//     return $list;
// })->name("login");



