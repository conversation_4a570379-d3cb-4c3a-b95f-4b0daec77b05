<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WarnPushLog extends Model
{
    use HasFactory;

    protected $table = 'warn_push_log';
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false;

    protected $fillable = [
        'id',
        'warn_id',
        'warn_time',
        'push_time',
        'push_user_id',
        'push_context',
        'push_response',
        'wx_time',
        'is_success',
        'create_time',
        'updata_time',
        'deleted',
        'view_time',
    ];
    public function warn_infolist()
    {
        return $this->belongsTo(WarnInfolist::class, 'warn_id');
    }
}
