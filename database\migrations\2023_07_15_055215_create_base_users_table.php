<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBaseUsersTable extends Migration
{
    public function up()
    {
        Schema::create('base_users', function (Blueprint $table) {
            $table->string('ID', 36)->primary()->comment('UUID');
            $table->string('USERNAME', 25)->comment('登录账号');
            $table->string('PASSWORD', 50)->comment('密码');
            $table->string('NAME', 50)->comment('昵称');
            $table->unsignedInteger('PRIMISE')->comment('所属权限');
            $table->string('TOWN', 25)->nullable()->comment('所属乡镇');
            $table->timestamp('CREATE_TIME')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('创建时间');
            $table->timestamp('UPDATA_TIME')->nullable()->default(null)->useCurrentOnUpdate()->comment('更新时间');
            $table->unsignedTinyInteger('DELETED')->default(0)->comment('0存在1删除');
            $table->string('WX_ID', 36)->nullable()->comment('微信openID');
            $table->unique('ID');
            $table->index('USERNAME');
            $table->index('PASSWORD');
            $table->index('NAME');
            $table->index('PRIMISE');
            $table->index('TOWN');
            $table->index('DELETED');
        });
    }

    public function down()
    {
        Schema::dropIfExists('base_users');
    }
}
