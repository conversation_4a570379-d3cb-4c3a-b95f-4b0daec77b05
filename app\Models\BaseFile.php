<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BaseFile extends Model
{
    use HasFactory;

    protected $table = 'base_files';
    protected $primaryKey = 'id';

    protected $fillable = [
        'tablename',
        'father_id',
        'path',
        'f_type',
        'createby',
        't',
    ];

    protected $casts = [
        'create_time' => 'datetime:Y-m-d H:i:s',
        'updata_time' => 'datetime:Y-m-d H:i:s',
    ];
}
