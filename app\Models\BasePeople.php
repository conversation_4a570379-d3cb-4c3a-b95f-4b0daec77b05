<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DB;

class BasePeople extends Model
{
    use HasFactory;

    protected $table = 'base_people';
    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'name',
        'idcard',
        'address',
        'town',
        'big_village',
        'phone',
        'relation_name',
        'relation_phone',
        'cadre_name',
        'cadre_phone',
        'collect_time',
        'lonlat',
        'address_info',
        'deleted'
    ];

    public function files()
    {
        return $this->hasMany(BaseFile::class, 'father_id')
            ->select(
                'base_files.id',
                'base_files.father_id',
                'base_files.path',
                // 'base_files.tablename',
                'base_files.f_type',
                'base_files.t',
                'base_files.CREATE_TIME'
            )->where(function ($query) {
                $query->where('tablename', '=', 'base_people')
                    ->where('f_type', '=', 'avatar');
            });
    }
    public function warn_infolist()
    {
        return $this->hasMany(WarnInfolist::class, 'people_id','ID')
            ->select("id","warn_rule_id","handle_state")->where(function ($query) {
                $query->where('deleted', '=', '0');
            });
    }
}
