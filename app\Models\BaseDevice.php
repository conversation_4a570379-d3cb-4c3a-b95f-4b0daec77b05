<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class BaseDevice extends Model
{
    use HasFactory;
    protected $table = 'base_device';
    protected $primaryKey = 'id';
    public $timestamps = false;
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    // 定义可批量赋值的属性
    protected $fillable = [
        'name',
        'device_id',
        'device_type',
        'address',
        'people_id',
        'add_uid',
        'bind_time',
    ];

    // 定义隐藏的属性
    protected $hidden = [
        'create_time',
        'updata_time',
    ];

    // 定义日期类型的属性
    protected $dates = [
        'create_time',
        'updata_time',
        'bind_time',
    ];
    public function getBindTimeAttribute($value)
    {
        return Carbon::parse($value)->format('Y-m-d H:i:s');
    }

    // 定义关联方法
    public function user()
    {
        return $this->belongsTo(BasePeople::class, 'people_id')
            ->with([
                'files' => function ($query) {
                    $query->select('path', 'f_type', 'father_id');
                }
            ]);
        ;
    }
    public function warn_infolist()
    {
        return $this->hasMany(WarnInfolist::class, 'device_id', 'device_id')
            ->select("id", "warn_title", "warn_decription", "warn_time", "handle_state", "device_id")
            ->where('deleted', '=', '0')
            ->orderBy("warn_time", "desc")
            ->limit(100);
    }
    public function devicelog()
    {
        return $this->hasMany(BaseDeviceLog::class, 'DeviceID', 'device_id')
            ->select("ID", "DeviceID", "HeartRate", "BreathRate", "HasPeople", "InTime")
            ->orderBy("InTime", "desc")
            ->limit(10);
    }
    public function lastDevicelog()
    {
        return $this->hasMany(BaseDeviceLog::class, 'DeviceID', 'device_id')
        ->select("InTime")
            ->orderBy('InTime', 'desc')
            ->limit(1);
    }
}
