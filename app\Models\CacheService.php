<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use DB;

class CacheService extends Model
{
    use HasFactory;
    public static function getCachedTjData($request)
    {
        $cacheKey = "warn_tj";
        $forever_cacheKey = "warn_tj_forever";
        // 尝试从缓存中获取数据
        $cachedData = Cache::get($cacheKey);

        // 如果缓存命中，则直接返回缓存数据
        if ($cachedData !== null) {
            return $cachedData;
        }

        // 使用分布式锁来防止缓存击穿
        $lockKey = $cacheKey . ':lock';
        $lockAcquired = Cache::lock($lockKey, 10)->get();

        if ($lockAcquired) {
            // 重新检查缓存，因为在等待锁的期间可能已经有其他进程设置了缓存
            $cachedData = Cache::get($cacheKey);

            if ($cachedData !== null) {
                Cache::lock($lockKey)->release(); // 释放锁
                return $cachedData;
            }

            // 如果缓存为空，则查询数据库并设置缓存


            $db = new BasePeople();
            if ($request->user()->TOWN != '') { //非超级管理员
                $where_town = "  TOWN='" . $request->user()->TOWN . "' ";
                $is_admin = false;
            } else {
                $where_town = " 1=1 ";
                $is_admin = true;
            }
            $paginator = $db->select(
                "id",
                "name",
                "address",
                "lonlat as zb"
            )
                ->with(['files'])
                ->withCount([
                    'warn_infolist as dcz' => function ($query) {
                        $query->where('handle_state', '=', '待处置');
                    },
                    'warn_infolist as ycz' => function ($query) {
                        $query->where('handle_state', '=', '已处置');
                    },
                    'warn_infolist as total',
                    'warn_infolist as value',
                ])
                ->where("deleted", "=", "0")
                ->whereRaw("lonlat is not null")
                ->whereRaw($where_town)
                ->get();
            //  dd(DB::getQueryLog());
            $res_sql = $paginator;

            //已处置未处置记录数
            $czTj = WarnInfolist::select('handle_state', DB::raw('count(1) as num'))
                ->when(!$is_admin, function ($query) use ($where_town) {
                    // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
                    return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
                        ->whereRaw($where_town);
                })
                // ->where('warn_title', '!=', '设备离线预警')
                ->groupBy('handle_state')
                ->get();
            //dd($czTj);
            // 初始化两个数组，用于存放待处置和已处置的结果
            $pendingArray = ['handle_state' => '待处置', 'num' => 0];
            $handledArray = ['handle_state' => '已处置', 'num' => 0];

            // 遍历查询结果，将结果放入相应的数组中
            foreach ($czTj as $result) {
                //dd($result->handle_state,$result->num);
                if ($result->handle_state == '待处置') {
                    $pendingArray['num'] = $result->num;
                } else {
                    $handledArray['num'] = $result->num;
                }
            }

            // 将两个数组按照 "待处置" 和 "已处置" 的顺序排序
            $sortedResult = [$pendingArray, $handledArray];
            //dd($sortedResult);
            //已处置的预警分类
            //DB::connection()->enableQueryLog();
            $yczFl = WarnInfolist::select('warn_title as name', DB::raw('count(1) as value'))
                ->when(!$is_admin, function ($query) use ($where_town) {
                    // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
                    return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
                        ->whereRaw($where_town);
                })
                ->where('handle_state', '已处置')
                // ->where('warn_title', '!=', '设备离线预警')
                ->groupBy('warn_title')
                ->get();
                //dd(DB::getQueryLog());
            //当月预警数
            $dyYjNum = WarnInfolist::select('warn_infolist.id', 'warn_infolist.people_id')
                ->when(!$is_admin, function ($query) use ($where_town) {
                    // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
                    return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
                        ->whereRaw($where_town);
                })
                // ->where('warn_title', '!=', '设备离线预警')
                ->whereRaw('MONTH(warn_time) = MONTH(NOW())')
                ->whereRaw(sql: 'YEAR(warn_time) = YEAR(NOW())')
                ->count();

            //每个月的预警数
            $every_month = WarnInfolist::select(DB::raw('MONTH(warn_time) as month'), DB::raw('COUNT(warn_infolist.id) as num'))
                ->when(!$is_admin, function ($query) use ($where_town) {
                    // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
                    return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
                        ->whereRaw($where_town);
                })
                ->whereYear('warn_time', '=', now()->year)
                // ->where('warn_title', '!=', '设备离线预警')
                ->groupBy(DB::raw('MONTH(warn_time)'))
                ->get();
            //每个月的已处置预警数
            $every_month_ycz = WarnInfolist::select(DB::raw('MONTH(handle_time) as month'), DB::raw('COUNT(warn_infolist.id) as num'))
                ->when(!$is_admin, function ($query) use ($where_town) {
                    // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
                    return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
                        ->whereRaw($where_town);
                })
                ->whereYear('handle_time', '=', now()->year)
                ->where('handle_state', '已处置')
                // ->where('warn_title', '!=', '设备离线预警')
                ->groupBy(DB::raw('MONTH(handle_time)'))
                ->get();
            //特困人员数
            $tkrrNum = BaseDevice::select('id')
                ->whereRaw($where_town)
                ->where('deleted', 0)
                ->count();
            //各预警分类数
            $yjFl = WarnInfolist::select('warn_title', DB::raw('count(1) as num'))
                ->when(!$is_admin, function ($query) use ($where_town) {
                    // 如果 $yourBoolValue 为 true，则添加 join 和 whereRaw 条件
                    return $query->join('base_people', 'warn_infolist.people_id', '=', 'base_people.id')
                        ->whereRaw($where_town);
                })

                //->where('handle_state', '待处置')
                // ->where('warn_title', '!=', '设备离线预警') // 添加这个条件
                ->groupBy('warn_title')
                ->get();

            if (!$res_sql) {
                $res['code'] = 101;
                $res['msg'] = "没有数据！";
            } else {
                $res['msg'] = "数据获取成功";
                $res['code'] = 0;
                $res['every_month'] = $every_month;
                $res['every_month_ycz'] = $every_month_ycz;
                $res['list'] = $res_sql;
                $res['czTj'] = $sortedResult;
                $res['yjFl'] = $yjFl;
                $res['dyYjNum'] = $dyYjNum;
                $res['tkrrNum'] = $tkrrNum;
                $res['yczFl'] = $yczFl;
                $res['limit'] = $res_sql->filter(function ($value, $key) {
                    return $value->value > 0;
                })->take(10)->values()->all();
            }
            $data = $res;
            // var_dump($data);

            Cache::put($cacheKey, $data, now()->addMinutes(5)); // 设置缓存并设置过期时间
            Cache::put($forever_cacheKey, $data); // 设置缓存并设置过期时间

            Cache::lock($lockKey)->release(); // 释放锁

            return $data;
        }

        // 如果无法获取锁，则使用旧的永久缓存数据
        return Cache::get($forever_cacheKey);
    }
}
